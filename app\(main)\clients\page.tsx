'use client';

import { useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import type { Contact, PaginationParams, SortParams, FilterParams, SearchParams } from '@/lib/types';
import { fetchClients, fetchSuppliers } from '@/lib/data-fetcher';
import { PaginatedTable } from '@/components/ui/paginated-table';
import { SearchInput } from '@/components/ui/search-input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Edit, Trash2 } from 'lucide-react';

type EntryType = 'client' | 'supplier';

const initialFormState: Omit<Contact, 'id'> = {
  name: '',
  phone: '',
  email: '',
};

export default function ClientsPage() {
  const { toast } = useToast();

  // State management
  const [clients, setClients] = useState<Contact[]>([]);
  const [suppliers, setSuppliers] = useState<Contact[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // On-demand data loading state
  const [paginatedClients, setPaginatedClients] = useState<Contact[]>([]);
  const [paginatedSuppliers, setPaginatedSuppliers] = useState<Contact[]>([]);
  const [isLoadingClients, setIsLoadingClients] = useState(false);
  const [isLoadingSuppliers, setIsLoadingSuppliers] = useState(false);
  const [clientsTotal, setClientsTotal] = useState(0);
  const [suppliersTotal, setSuppliersTotal] = useState(0);

  // Pagination and search state
  const [clientsPage, setClientsPage] = useState(1);
  const [suppliersPage, setSuppliersPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [clientsSearch, setClientsSearch] = useState('');
  const [suppliersSearch, setSuppliersSearch] = useState('');
  const [clientsSort, setClientsSort] = useState<SortParams>({ field: 'name', direction: 'asc' });
  const [suppliersSort, setSuppliersSort] = useState<SortParams>({ field: 'name', direction: 'asc' });

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [dialogType, setDialogType] = useState<EntryType>('client');
  const [editingEntry, setEditingEntry] = useState<Contact | null>(null);
  const [entryToDelete, setEntryToDelete] = useState<{
    entry: Contact;
    type: EntryType;
  } | null>(null);
  const [formData, setFormData] =
    useState<Omit<Contact, 'id'>>(initialFormState);

  // API functions for legacy support (keeping for compatibility)
  const fetchClientsSimple = async () => {
    try {
      const response = await fetch('/api/clients-simple');
      if (!response.ok) throw new Error('فشل في تحميل العملاء');
      const data = await response.json();
      setClients(data);
    } catch (error) {
      console.error('خطأ في تحميل العملاء:', error);
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'فشل في تحميل العملاء'
      });
    }
  };

  const fetchSuppliersSimple = async () => {
    try {
      const response = await fetch('/api/suppliers-simple');
      if (!response.ok) throw new Error('فشل في تحميل الموردين');
      const data = await response.json();
      setSuppliers(data);
    } catch (error) {
      console.error('خطأ في تحميل الموردين:', error);
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'فشل في تحميل الموردين'
      });
    }
  };

  const fetchData = async () => {
    setIsLoading(true);
    await Promise.all([fetchClientsSimple(), fetchSuppliersSimple()]);
    setIsLoading(false);
  };

  // New on-demand data fetching functions
  const fetchPaginatedClients = async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    sort?: SortParams;
  }) => {
    setIsLoadingClients(true);
    try {
      const response = await fetchClients({
        pagination: {
          page: params?.page || clientsPage,
          limit: params?.limit || pageSize,
        },
        search: params?.search ? { query: params.search } : undefined,
        sort: params?.sort || clientsSort,
      });

      setPaginatedClients(response.data as Contact[]);
      setClientsTotal(response.pagination.total);
    } catch (error) {
      console.error('Error fetching clients:', error);
      toast({
        variant: 'destructive',
        title: 'خطأ في جلب البيانات',
        description: 'حدث خطأ أثناء جلب بيانات العملاء',
      });
    } finally {
      setIsLoadingClients(false);
    }
  };

  const fetchPaginatedSuppliers = async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    sort?: SortParams;
  }) => {
    setIsLoadingSuppliers(true);
    try {
      const response = await fetchSuppliers({
        pagination: {
          page: params?.page || suppliersPage,
          limit: params?.limit || pageSize,
        },
        search: params?.search ? { query: params.search } : undefined,
        sort: params?.sort || suppliersSort,
      });

      setPaginatedSuppliers(response.data as Contact[]);
      setSuppliersTotal(response.pagination.total);
    } catch (error) {
      console.error('Error fetching suppliers:', error);
      toast({
        variant: 'destructive',
        title: 'خطأ في جلب البيانات',
        description: 'حدث خطأ أثناء جلب بيانات الموردين',
      });
    } finally {
      setIsLoadingSuppliers(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchData();
  }, []);

  // Load paginated clients when parameters change
  useEffect(() => {
    fetchPaginatedClients();
  }, [clientsPage, pageSize, clientsSearch, clientsSort]);

  // Load paginated suppliers when parameters change
  useEffect(() => {
    fetchPaginatedSuppliers();
  }, [suppliersPage, pageSize, suppliersSearch, suppliersSort]);

  // Temporary permissions (remove when auth system is implemented)
  const permissions = { create: true, edit: true, delete: true };

  // Clients table columns configuration
  const clientsColumns = [
    {
      key: 'name',
      label: 'الاسم',
      sortable: true,
      render: (client: Contact) => (
        <span className="font-medium">{client.name}</span>
      ),
    },
    {
      key: 'phone',
      label: 'رقم الهاتف',
      sortable: true,
      render: (client: Contact) => (
        <span className="font-mono text-sm">{client.phone || '-'}</span>
      ),
    },
    {
      key: 'email',
      label: 'البريد الإلكتروني',
      sortable: true,
      render: (client: Contact) => (
        <span className="text-sm">{client.email || '-'}</span>
      ),
    },
    {
      key: 'actions',
      label: 'الإجراءات',
      sortable: false,
      render: (client: Contact) => (
        <div className="flex gap-2">
          {permissions?.edit && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleOpenDialog('client', client)}
              className="h-8 px-2"
            >
              <Edit className="h-4 w-4" />
            </Button>
          )}
          {permissions?.delete && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setEntryToDelete({ entry: client, type: 'client' })}
              className="h-8 px-2 text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      ),
    },
  ];

  // Suppliers table columns configuration
  const suppliersColumns = [
    {
      key: 'name',
      label: 'الاسم',
      sortable: true,
      render: (supplier: Contact) => (
        <span className="font-medium">{supplier.name}</span>
      ),
    },
    {
      key: 'phone',
      label: 'رقم الهاتف',
      sortable: true,
      render: (supplier: Contact) => (
        <span className="font-mono text-sm">{supplier.phone || '-'}</span>
      ),
    },
    {
      key: 'email',
      label: 'البريد الإلكتروني',
      sortable: true,
      render: (supplier: Contact) => (
        <span className="text-sm">{supplier.email || '-'}</span>
      ),
    },
    {
      key: 'actions',
      label: 'الإجراءات',
      sortable: false,
      render: (supplier: Contact) => (
        <div className="flex gap-2">
          {permissions?.edit && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleOpenDialog('supplier', supplier)}
              className="h-8 px-2"
            >
              <Edit className="h-4 w-4" />
            </Button>
          )}
          {permissions?.delete && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setEntryToDelete({ entry: supplier, type: 'supplier' })}
              className="h-8 px-2 text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      ),
    },
  ];

  const handleOpenDialog = (type: EntryType, entry: Contact | null) => {
    setDialogType(type);
    if (entry) {
      setEditingEntry(entry);
      setFormData(entry);
    } else {
      setEditingEntry(null);
      setFormData(initialFormState);
    }
    setIsDialogOpen(true);
  };

  const handleDialogClose = () => {
    setIsDialogOpen(false);
    setEditingEntry(null);
    setFormData(initialFormState);
  };

  const handleSave = async () => {
    const typeName = dialogType === 'client' ? 'العميل' : 'المورد';

    // التحقق من أن الاسم مطلوب
    if (!formData.name.trim()) {
      toast({
        variant: 'destructive',
        title: 'بيانات ناقصة',
        description: `يرجى إدخال اسم ${typeName}.`,
      });
      return;
    }

    try {
      // إضافة دوال API هنا
      // سيتم تحديثها لاحقاً
      if (editingEntry) {
        // updateContact({ ...formData, id: editingEntry.id }, dialogType);
        toast({
          title: 'تم التحديث',
          description: `تم تحديث بيانات ${typeName} بنجاح.`,
        });
      } else {
        // addContact(formData, dialogType);
        toast({
          title: 'تمت الإضافة',
          description: `تمت إضافة ${typeName} بنجاح.`,
        });
      }

      // Refresh both legacy and paginated data
      await fetchData();
      if (dialogType === 'client') {
        await fetchPaginatedClients();
      } else {
        await fetchPaginatedSuppliers();
      }

      handleDialogClose();
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'خطأ في الحفظ',
        description: error instanceof Error ? error.message : 'حدث خطأ غير متوقع',
      });
    }
  };

  const handleDelete = async () => {
    if (entryToDelete) {
      const typeName = entryToDelete.type === 'client' ? 'العميل' : 'المورد';

      try {
        // فحص العلاقات قبل الحذف
        const relationCheck = entryToDelete.type === 'client'
          ? checkClientRelations(entryToDelete.entry.id)
          : checkSupplierRelations(entryToDelete.entry.id);

        if (!relationCheck.canDelete) {
          toast({
            variant: 'destructive',
            title: 'لا يمكن الحذف',
            description: relationCheck.reason +
              (relationCheck.relatedOperations ?
                '\nالعمليات المرتبطة: ' + relationCheck.relatedOperations.join(', ') :
                ''),
          });
          setEntryToDelete(null);
          return;
        }

        deleteContact(entryToDelete.entry.id, entryToDelete.type);
        toast({
          title: 'تم الحذف',
          description: `تم حذف ${typeName} بنجاح.`,
          variant: 'destructive',
        });

        // Refresh both legacy and paginated data
        await fetchData();
        if (entryToDelete.type === 'client') {
          await fetchPaginatedClients();
        } else {
          await fetchPaginatedSuppliers();
        }

        setEntryToDelete(null);
      } catch (error) {
        toast({
          variant: 'destructive',
          title: 'خطأ في الحذف',
          description: error instanceof Error ? error.message : 'حدث خطأ غير متوقع',
        });
        setEntryToDelete(null);
      }
    }
  };

  const renderTable = (data: Contact[], type: EntryType) => (
    <div className="rounded-lg border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>الاسم</TableHead>
            <TableHead>رقم الهاتف</TableHead>
            <TableHead>البريد الإلكتروني</TableHead>
            <TableHead>إجراءات</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.map((entry) => (
            <TableRow key={entry.id}>
              <TableCell className="font-medium">{entry.name}</TableCell>
              <TableCell>{entry.phone}</TableCell>
              <TableCell>{entry.email}</TableCell>
              <TableCell>
                <div className="flex gap-2">
                  {permissions?.edit && (
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleOpenDialog(type, entry)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  )}
                  {permissions?.delete && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="text-destructive hover:text-destructive"
                      onClick={() => setEntryToDelete({ entry, type })}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );

  return (
    <>
      <Tabs defaultValue="clients" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="clients">العملاء</TabsTrigger>
          <TabsTrigger value="suppliers">الموردين</TabsTrigger>
        </TabsList>
        <TabsContent value="clients">
          <div className="flex flex-col gap-4">
            {/* Summary Card */}
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-xl">قائمة العملاء</CardTitle>
                    <p className="text-sm text-muted-foreground mt-1">
                      إجمالي العملاء: {clientsTotal}
                    </p>
                  </div>
                  {permissions?.create && (
                    <Button onClick={() => handleOpenDialog('client', null)}>
                      إضافة عميل جديد
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                {/* Search */}
                <div className="mb-4">
                  <SearchInput
                    value={clientsSearch}
                    onChange={setClientsSearch}
                    placeholder="البحث في العملاء..."
                    className="max-w-md"
                  />
                </div>

                {/* Clients Table */}
                <PaginatedTable
                  data={paginatedClients}
                  columns={clientsColumns}
                  loading={isLoadingClients}
                  pagination={{
                    page: clientsPage,
                    limit: pageSize,
                    total: clientsTotal,
                  }}
                  sorting={clientsSort}
                  onPageChange={setClientsPage}
                  onPageSizeChange={setPageSize}
                  onSortChange={setClientsSort}
                  emptyMessage="لا توجد عملاء"
                  className="min-h-[400px]"
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="suppliers">
          <div className="flex flex-col gap-4">
            {/* Summary Card */}
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-xl">قائمة الموردين</CardTitle>
                    <p className="text-sm text-muted-foreground mt-1">
                      إجمالي الموردين: {suppliersTotal}
                    </p>
                  </div>
                  {permissions?.create && (
                    <Button onClick={() => handleOpenDialog('supplier', null)}>
                      إضافة مورد جديد
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                {/* Search */}
                <div className="mb-4">
                  <SearchInput
                    value={suppliersSearch}
                    onChange={setSuppliersSearch}
                    placeholder="البحث في الموردين..."
                    className="max-w-md"
                  />
                </div>

                {/* Suppliers Table */}
                <PaginatedTable
                  data={paginatedSuppliers}
                  columns={suppliersColumns}
                  loading={isLoadingSuppliers}
                  pagination={{
                    page: suppliersPage,
                    limit: pageSize,
                    total: suppliersTotal,
                  }}
                  sorting={suppliersSort}
                  onPageChange={setSuppliersPage}
                  onPageSizeChange={setPageSize}
                  onSortChange={setSuppliersSort}
                  emptyMessage="لا توجد موردين"
                  className="min-h-[400px]"
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Add/Edit Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent
          className="sm:max-w-[425px]"
          onInteractOutside={(e) => e.preventDefault()}
          onCloseAutoFocus={handleDialogClose}
        >
          <DialogHeader>
            <DialogTitle>
              إضافة {dialogType === 'client' ? 'عميل' : 'مورد'} جديد
            </DialogTitle>
            <DialogDescription>
              أدخل بيانات {dialogType === 'client' ? 'العميل' : 'المورد'} الجديد
              هنا.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                الاسم *
              </Label>
              <Input
                id="name"
                className="col-span-3"
                value={formData.name}
                onChange={(e) =>
                  setFormData({ ...formData, name: e.target.value })
                }
                placeholder="أدخل الاسم"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="phone" className="text-right">
                الهاتف (اختياري)
              </Label>
              <Input
                id="phone"
                className="col-span-3"
                value={formData.phone}
                onChange={(e) =>
                  setFormData({ ...formData, phone: e.target.value })
                }
                placeholder="أدخل رقم الهاتف"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="email" className="text-right">
                البريد الإلكتروني (اختياري)
              </Label>
              <Input
                id="email"
                type="email"
                className="col-span-3"
                value={formData.email}
                onChange={(e) =>
                  setFormData({ ...formData, email: e.target.value })
                }
                placeholder="أدخل البريد الإلكتروني"
              />
            </div>
          </div>
          <DialogFooter>
            <Button onClick={handleSave}>حفظ</Button>
            <DialogClose asChild>
              <Button variant="outline">إلغاء</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        open={!!entryToDelete}
        onOpenChange={() => setEntryToDelete(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="text-red-600">
              ⚠️ تأكيد حذف {entryToDelete?.type === 'client' ? 'العميل' : 'المورد'}
            </AlertDialogTitle>
            <AlertDialogDescription asChild>
              <div className="space-y-2">
                <div className="text-gray-700">
                  هذا الإجراء لا يمكن التراجع عنه. سيؤدي هذا إلى حذف بيانات
                  <span className="font-semibold text-red-600">
                    {entryToDelete?.type === 'client' ? ' العميل ' : ' المورد '}
                    "{entryToDelete?.entry.name}"
                  </span>
                  بشكل دائم.
                </div>
                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 mt-3">
                  <div className="text-yellow-800 text-sm">
                    <strong>تنبيه:</strong> سيتم فحص العلاقات المرتبطة قبل الحذف.
                    {entryToDelete?.type === 'client'
                      ? ' إذا كان للعميل مبيعات أو مرتجعات، فلن يتم الحذف.'
                      : ' إذا كان للمورد أوامر توريد، فلن يتم الحذف.'
                    }
                  </div>
                </div>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="bg-gray-100 hover:bg-gray-200">
              إلغاء
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              متابعة الحذف
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
