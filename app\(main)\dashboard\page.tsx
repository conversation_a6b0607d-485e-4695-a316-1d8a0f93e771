'use client';
import { useStore } from '@/context/store';
import { fetchDevices, fetchSales } from '@/lib/data-fetcher';
import { PaginatedTable } from '@/components/ui/paginated-table';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid } from 'recharts';
import {
  Smartphone,
  CheckCircle,
  Wrench,
  DollarSign,
  Package,
} from 'lucide-react';
import { format } from 'date-fns';
import { useState, useMemo, useEffect } from 'react';
import type { ActivityLog, Device, Sale, PaginationParams, SortParams } from '@/lib/types';

const chartData = [
  { month: 'يناير', sales: 186 },
  { month: 'فبراير', sales: 305 },
  { month: 'مارس', sales: 237 },
  { month: 'أبريل', sales: 273 },
  { month: 'مايو', sales: 209 },
  { month: 'يونيو', sales: 214 },
];

const chartConfig = {
  sales: {
    label: 'المبيعات',
    color: 'hsl(var(--primary))',
  },
};

export default function DashboardPage() {
  const { devices, sales, activities } = useStore();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('all');

  // On-demand data loading state
  const [recentDevices, setRecentDevices] = useState<Device[]>([]);
  const [recentSales, setRecentSales] = useState<Sale[]>([]);
  const [isLoadingDevices, setIsLoadingDevices] = useState(false);
  const [isLoadingSales, setIsLoadingSales] = useState(false);
  const [devicesTotal, setDevicesTotal] = useState(0);
  const [salesTotal, setSalesTotal] = useState(0);

  // Fetch recent devices for dashboard
  const fetchRecentDevices = async () => {
    setIsLoadingDevices(true);
    try {
      const response = await fetchDevices({
        pagination: { page: 1, limit: 5 },
        sort: { field: 'createdAt', direction: 'desc' },
      });
      setRecentDevices(response.data);
      setDevicesTotal(response.total);
    } catch (error) {
      console.error('Error fetching recent devices:', error);
    } finally {
      setIsLoadingDevices(false);
    }
  };

  // Fetch recent sales for dashboard
  const fetchRecentSales = async () => {
    setIsLoadingSales(true);
    try {
      const response = await fetchSales({
        pagination: { page: 1, limit: 5 },
        sort: { field: 'saleDate', direction: 'desc' },
      });
      setRecentSales(response.data);
      setSalesTotal(response.total);
    } catch (error) {
      console.error('Error fetching recent sales:', error);
    } finally {
      setIsLoadingSales(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchRecentDevices();
    fetchRecentSales();
  }, []);

  const totalDevices = devices.length;
  const readyForSale = devices.filter((d) => d.status === 'متاح للبيع').length;
  const inMaintenance = devices.filter(
    (d) => d.status === 'تحتاج صيانة',
  ).length;
  const totalSalesValue = sales.reduce((acc, sale) => {
    return acc + sale.items.reduce((itemAcc, item) => itemAcc + item.price, 0);
  }, 0);

  const getStatusInfo = (type: string) => {
    switch (type) {
      case 'sale':
        return {
          text: 'بيع',
          className: 'bg-green-500/20 text-green-400 border-green-500/20',
        };
      case 'return':
        return {
          text: 'إرجاع',
          className: 'bg-red-500/20 text-red-400 border-red-500/20',
        };
      case 'maintenance':
        return {
          text: 'صيانة',
          className: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/20',
        };
      case 'supply':
        return {
          text: 'توريد',
          className: 'bg-blue-500/20 text-blue-400 border-blue-500/20',
        };
      case 'evaluation':
        return {
          text: 'تقييم',
          className: 'bg-purple-500/20 text-purple-400 border-purple-500/20',
        };
      case 'transfer':
        return {
          text: 'تحويل',
          className: 'bg-indigo-500/20 text-indigo-400 border-indigo-500/20',
        };
      case 'request':
        return {
          text: 'طلب',
          className: 'bg-pink-500/20 text-pink-400 border-pink-500/20',
        };
      case 'message':
        return {
          text: 'رسالة',
          className: 'bg-teal-500/20 text-teal-400 border-teal-500/20',
        };
      default:
        return {
          text: 'نشاط',
          className: 'bg-gray-500/20 text-gray-400 border-gray-500/20',
        };
    }
  };

  const filteredActivities = useMemo(() => {
    return activities.filter((activity) => {
      const matchesSearch =
        activity.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        activity.username.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesType = filterType === 'all' || activity.type === filterType;
      return matchesSearch && matchesType;
    });
  }, [activities, searchTerm, filterType]);

  // Recent devices table columns
  const recentDevicesColumns = [
    {
      key: 'imei',
      label: 'IMEI',
      sortable: false,
      render: (device: Device) => (
        <span className="font-mono text-sm">{device.imei}</span>
      ),
    },
    {
      key: 'model',
      label: 'الموديل',
      sortable: false,
      render: (device: Device) => (
        <span className="font-medium">{device.model}</span>
      ),
    },
    {
      key: 'status',
      label: 'الحالة',
      sortable: false,
      render: (device: Device) => (
        <Badge variant={device.status === 'متاح للبيع' ? 'default' : 'secondary'}>
          {device.status}
        </Badge>
      ),
    },
  ];

  // Recent sales table columns
  const recentSalesColumns = [
    {
      key: 'saleId',
      label: 'رقم البيع',
      sortable: false,
      render: (sale: Sale) => (
        <span className="font-mono text-sm">{sale.saleId}</span>
      ),
    },
    {
      key: 'clientName',
      label: 'العميل',
      sortable: false,
      render: (sale: Sale) => (
        <span className="font-medium">{sale.clientName || 'غير محدد'}</span>
      ),
    },
    {
      key: 'totalValue',
      label: 'القيمة',
      sortable: false,
      render: (sale: Sale) => {
        const total = sale.items?.reduce((acc, item) => acc + (item.price || 0), 0) || 0;
        return <span className="font-medium">${total.toFixed(2)}</span>;
      },
    },
  ];

  return (
    <div className="flex flex-col gap-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              إجمالي الأجهزة
            </CardTitle>
            <Smartphone className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalDevices}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">جاهز للبيع</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{readyForSale}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">تحتاج صيانة</CardTitle>
            <Wrench className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{inMaintenance}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              إجمالي المبيعات
            </CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${totalSalesValue.toFixed(2)}
            </div>
          </CardContent>
        </Card>
      </div>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>نظرة عامة على المبيعات</CardTitle>
          </CardHeader>
          <CardContent className="pl-2">
            <ChartContainer config={chartConfig} className="h-[300px] w-full">
              <BarChart data={chartData} accessibilityLayer>
                <CartesianGrid vertical={false} />
                <XAxis
                  dataKey="month"
                  tickLine={false}
                  tickMargin={10}
                  axisLine={false}
                  tickFormatter={(value) => value.slice(0, 3)}
                />
                <YAxis
                  tickLine={false}
                  axisLine={false}
                  tickMargin={10}
                  orientation="right"
                />
                <ChartTooltip
                  cursor={false}
                  content={<ChartTooltipContent />}
                />
                <Bar dataKey="sales" fill="var(--color-sales)" radius={8} />
              </BarChart>
            </ChartContainer>
          </CardContent>
        </Card>
        <Card className="col-span-4 lg:col-span-3">
          <CardHeader>
            <CardTitle>سجل الأنشطة</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4 mb-4">
              <Input
                placeholder="بحث بالوصف أو اسم المستخدم..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="flex-grow"
              />
              <Select
                value={filterType}
                onValueChange={(value) => setFilterType(value)}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="تصفية حسب النوع" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الأنواع</SelectItem>
                  <SelectItem value="sale">بيع</SelectItem>
                  <SelectItem value="return">إرجاع</SelectItem>
                  <SelectItem value="maintenance">صيانة</SelectItem>
                  <SelectItem value="supply">توريد</SelectItem>
                  <SelectItem value="evaluation">تقييم</SelectItem>
                  <SelectItem value="transfer">تحويل</SelectItem>
                  <SelectItem value="request">طلب</SelectItem>
                  <SelectItem value="message">رسالة</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>النشاط</TableHead>
                  <TableHead>الوصف</TableHead>
                  <TableHead>المستخدم</TableHead>
                  <TableHead>التاريخ</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredActivities.length > 0 ? (
                  filteredActivities.map((activity) => (
                    <TableRow key={activity.id}>
                      <TableCell>
                        <Badge
                          variant="outline"
                          className={getStatusInfo(activity.type).className}
                        >
                          {getStatusInfo(activity.type).text}
                        </Badge>
                      </TableCell>
                      <TableCell className="font-medium">
                        {activity.description}
                      </TableCell>
                      <TableCell>{activity.username}</TableCell>
                      <TableCell>
                        {format(activity.date, 'yyyy-MM-dd HH:mm')}
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={4} className="h-24 text-center">
                      لا توجد أنشطة مطابقة.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>

      {/* Recent Data Section */}
      <div className="grid gap-4 md:grid-cols-2">
        {/* Recent Devices */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>الأجهزة الحديثة</span>
              <span className="text-sm font-normal text-muted-foreground">
                آخر 5 أجهزة
              </span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoadingDevices ? (
              <div className="flex justify-center py-8">
                <LoadingSpinner size="md" />
              </div>
            ) : (
              <PaginatedTable
                data={recentDevices}
                columns={recentDevicesColumns}
                loading={false}
                pagination={{
                  page: 1,
                  limit: 5,
                  total: devicesTotal,
                }}
                sorting={{ field: 'createdAt', direction: 'desc' }}
                onPageChange={() => {}}
                onPageSizeChange={() => {}}
                onSortChange={() => {}}
                emptyMessage="لا توجد أجهزة"
                showPagination={false}
                className="min-h-[200px]"
              />
            )}
          </CardContent>
        </Card>

        {/* Recent Sales */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>المبيعات الحديثة</span>
              <span className="text-sm font-normal text-muted-foreground">
                آخر 5 مبيعات
              </span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoadingSales ? (
              <div className="flex justify-center py-8">
                <LoadingSpinner size="md" />
              </div>
            ) : (
              <PaginatedTable
                data={recentSales}
                columns={recentSalesColumns}
                loading={false}
                pagination={{
                  page: 1,
                  limit: 5,
                  total: salesTotal,
                }}
                sorting={{ field: 'saleDate', direction: 'desc' }}
                onPageChange={() => {}}
                onPageSizeChange={() => {}}
                onSortChange={() => {}}
                emptyMessage="لا توجد مبيعات"
                showPagination={false}
                className="min-h-[200px]"
              />
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
